import 'dart:math';
import 'dart:typed_data' show Uint8List;
import 'package:encrypt/encrypt.dart';

class EncryptionHelper {
  static final _encryptionKey = 'MOoRZhMT3c5yrE1A';

  static Map<String, String> encryptCustomerCode(String customerCode) {
    // Ensure the key is exactly 16 bytes (128 bits)
    final key = Key.fromUtf8(_encryptionKey.substring(0, 16));

    // Generate a random 16-byte IV
    final ivBytes = _generateRandomBytes(16);
    final iv = IV(ivBytes);

    // Create the encrypter with AES-128 in CBC mode with PKCS7 padding
    final encrypter = Encrypter(AES(key, mode: AESMode.cbc, padding: 'PKCS7'));

    // Encrypt the customer code
    final encrypted = encrypter.encrypt(customerCode, iv: iv);

    return {
      'encryptedCustomerCode': encrypted.base64,
      'iv': iv.base64,
    };
  }

  static Uint8List _generateRandomBytes(int length) {
    final random = Random.secure();
    final bytes = List<int>.generate(length, (_) => random.nextInt(256));
    return Uint8List.fromList(bytes);
  }
}
