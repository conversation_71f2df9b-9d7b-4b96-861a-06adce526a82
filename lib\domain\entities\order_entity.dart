import 'package:rozana/domain/entities/order_item_entity.dart';
import 'package:rozana/domain/entities/order_timeline_entity.dart';
import 'package:rozana/data/models/adress_model.dart';

/// Domain entity representing an order
/// Pure business object without external dependencies
class OrderEntity {
  final String id;
  final String customerId;
  final String customerName;
  final String status;
  final DateTime orderDate;
  final DateTime? deliveryDate;
  final String? estimatedDeliveryTime;
  final double totalAmount;
  final double subtotal;
  final double tax;
  final double deliveryFee;
  final double discount;
  final String paymentMethod;
  final AddressModel? deliveryAddress;
  final List<OrderItemEntity> items;
  final List<OrderTimelineEntity> orderTimeline;
  final bool canCancel;
  final bool canReorder;

  OrderEntity({
    required this.id,
    required this.customerId,
    required this.customerName,
    required this.status,
    required this.orderDate,
    this.deliveryDate,
    this.estimatedDeliveryTime,
    required this.totalAmount,
    required this.subtotal,
    required this.tax,
    required this.deliveryFee,
    required this.discount,
    required this.paymentMethod,
    this.deliveryAddress,
    required this.items,
    required this.orderTimeline,
    required this.canCancel,
    required this.canReorder,
  });

  /// Get total number of items in the order
  int get totalItems => items.fold(0, (sum, item) => sum + item.quantity);

  /// Check if order is in progress (can be tracked)
  bool get isInProgress {
    return status == 'pending' ||
        status == 'confirmed' ||
        status == 'preparing' ||
        status == '10' ||
        status == '24' ||
        status == '25' ||
        status == '26';
  }

  /// Check if order is completed
  bool get isCompleted => status == 'delivered' || status == '11';

  /// Check if order is cancelled
  bool get isCancelled {
    const cancelledStatuses = [
      'cancelled',
      '14', // Numeric cancelled status
      'Cancelled',
      'CANCELLED',
      'WMS Cancelled',
      'Cancel',
      'CANCEL',
    ];
    
    final result = cancelledStatuses.contains(status);
    
    return result;
  }

  /// Get order status display text
  String get statusDisplayText {
    switch (status) {
      case 'pending':
      case '10':
        return 'Order Placed';
      case 'confirmed':
      case '24':
        return 'Confirmed';
      case 'preparing':
      case '25':
      case '26':
        return 'Preparing';
      case 'out_for_delivery':
      case '27':
        return 'Out for Delivery';
      case 'delivered':
      case '11':
        return 'Delivered';
      case 'cancelled':
      case '14':
        return 'Cancelled';
      default:
        return status.toUpperCase();
    }
  }

  /// Get order status color based on status
  String get statusColor {
    switch (status) {
      case 'pending':
      case '10':
        return '#FFA500'; // Orange
      case 'confirmed':
      case '24':
        return '#2196F3'; // Blue
      case 'preparing':
      case '25':
      case '26':
        return '#FF9800'; // Amber
      case 'out_for_delivery':
      case '27':
        return '#9C27B0'; // Purple
      case 'delivered':
      case '11':
        return '#4CAF50'; // Green
      case 'cancelled':
      case '14':
        return '#F44336'; // Red
      default:
        return '#757575'; // Grey
    }
  }

  /// Get the latest timeline entry
  OrderTimelineEntity? get latestTimelineEntry {
    if (orderTimeline.isEmpty) return null;
    return orderTimeline.last;
  }

  /// Get formatted order date
  String get formattedOrderDate {
    return '${orderDate.day}/${orderDate.month}/${orderDate.year}';
  }

  /// Get formatted delivery date
  String? get formattedDeliveryDate {
    if (deliveryDate == null) return null;
    return '${deliveryDate!.day}/${deliveryDate!.month}/${deliveryDate!.year}';
  }
}
