import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:rozana/core/dependency_injection/di_container.dart';
import 'package:rozana/core/network/api_client.dart';
import 'package:rozana/core/network/service_api_client.dart';
import 'package:rozana/core/network/api_endpoints.dart';
import 'package:rozana/domain/repositories/order_repository_interface.dart';
import 'package:rozana/domain/entities/order_entity.dart';
import 'package:rozana/data/models/order_model.dart';
import 'package:rozana/data/mappers/order_mapper.dart';
import 'package:rozana/core/services/app_preferences_service.dart';
import 'package:rozana/features/search/services/typesense_service.dart';

class OrderRepositoryImpl implements OrderRepositoryInterface {
  /// Get current user's customer name from local storage
  Map<String, dynamic> _getCurrentUserData() {
    try {
      // Get user data from SharedPreferences
      final userDataJson = AppPreferences.getUserdata();
      if (userDataJson != null && userDataJson.isNotEmpty) {
        return jsonDecode(userDataJson);
      }
      return {};
    } catch (e) {
      return {};
    }
  }

  /// Get current facility/store details
  Map<String, String> _getCurrentFacilityDetails() {
    try {
      // Get the TypesenseService instance
      final typesenseService = getIt<TypesenseService>();
      final storeId = typesenseService.currentStoreId;
      final storeName = typesenseService.currentStoreName;

      return {
        'id': storeId ?? '1',
        'name': storeName ?? 'demo',
      };
    } catch (e) {
      return {'id': '1', 'name': 'demo'};
    }
  }

  @override
  Future<Response?> createOrder({
    required num totalAmount,
    required List<Map<String, dynamic>> items,
    Map<String, dynamic>? address,
    String? paymentMethod,
    Map<String, dynamic>? paymentDetails,
    String? facilityName,
  }) async {
    // Get customer details
    final userData = _getCurrentUserData();
    final customerId = userData['uid'] ?? 'CUST-001';
    final customerName = userData['displayName'] ?? 'TEST';

    // Get facility/store details
    final facilityDetails = _getCurrentFacilityDetails();
    final facilityId = facilityDetails['id'] ?? '1';
    // Use the provided facilityName parameter if available, otherwise use the default from facility details
    final String storeNameToUse =
        facilityName ?? facilityDetails['name'] ?? 'demo';

    // Build the order payload with the new structure
    final orderPayload = {
      'customer_id': customerId,
      'customer_name': customerName,
      'facility_id': facilityId,
      'facility_name': storeNameToUse,
      'status': 'pending',
      'total_amount': totalAmount,
      'items': items
    };

    // Add address if provided
    if (address != null) {
      // Map to the expected format
      orderPayload['address'] = {
        'full_name': address['name'] ?? userData['displayName'] ?? '',
        'phone_number': address['phone'] ?? userData['phoneNumber'] ?? '',
        'address_line1': address['addressLine1'] ?? '',
        'address_line2': address['addressLine2'] ?? '',
        'city': address['city'] ?? '',
        'state': address['state'] ?? '',
        'postal_code': address['pincode'] ?? '',
        'country': 'India', // Default to India
        'type_of_address': ((address['addressType']?.toString().toLowerCase() ==
                    'home') ||
                (address['addressType']?.toString().toLowerCase() == 'work'))
            ? address['addressType']
            : 'other',
        // Add latitude and longitude from the selected address
        'latitude': address['latitude'],
        'longitude': address['longitude'],
      };
    }

    orderPayload.addAll(paymentDetails!);

    // Using ServiceApiClient - automatically routes to OMS for order creation
    return await ServiceApiClient.post(
      endUrl: EndUrl.createOrder,
      data: orderPayload,
      tag: 'CreateOrder',
    );
  }

  @override
  Future<List<OrderEntity>> getOrderHistory({
    String customerId = 'customer_123',
    String status = '',
    int page = 0,
    int pageSize = 10,
    bool refresh = false,
  }) async {
    try {
      // Use ServiceApiClient to call OMS API directly
      final response = await ServiceApiClient.getFromOms(
        endUrl: EndUrl.getOrderHistory,
        tag: 'GetOrderHistory',
      );

      if (response?.data == null) {
        throw Exception('No data received from API');
      }

      // The API returns an array of orders directly
      final List<dynamic> ordersData = response!.data as List<dynamic>;

      // Convert to models and then to entities
      final orderModels = ordersData
          .map((json) => OrderModel.fromJson(json as Map<String, dynamic>))
          .toList();

      // Apply client-side filtering if status is provided (since API might not support it)
      List<OrderModel> filteredOrders = orderModels;
      if (status.isNotEmpty) {
        filteredOrders = orderModels
            .where(
                (order) => order.status?.toLowerCase() == status.toLowerCase())
            .toList();
      }

      // Apply client-side pagination since API returns all orders
      final start = page * pageSize;
      final end = start + pageSize;

      if (start >= filteredOrders.length) {
        return [];
      }

      final paginatedOrders = filteredOrders.sublist(
          start, end > filteredOrders.length ? filteredOrders.length : end);

      return OrderMapper.toEntityList(paginatedOrders);
    } catch (e) {
      if (e.toString() == ApiErrorMessages.notFound ||
          (e.toString() == 'No orders found')) {
        return [];
      } else {
        throw Exception('Failed to fetch order history: $e');
      }
    }
  }

  @override
  Future<List<dynamic>> getPreviousOrders({
    String customerId = 'customer_123',
    String status = '',
    int page = 0,
    int pageSize = 10,
    bool refresh = false,
  }) async {
    try {
      // Use ServiceApiClient to call OMS API directly
      final response = await ServiceApiClient.getFromOms(
        endUrl: EndUrl.previousOrder,
        tag: 'GetPreviousOrder',
      );

      if (response?.data == null) {
        throw Exception('No data received from API');
      }

      // The API returns an array of orders directly
      final List<dynamic> skus = (response!.data['products'] ?? []);

      return skus;
    } catch (e) {
      if (e.toString() == ApiErrorMessages.notFound) {
        return [];
      } else {
        throw Exception('Failed to fetch order history: $e');
      }
    }
  }

  Future<List<dynamic>> getSKUDataFromTS(List<dynamic> skuList) async {
    try {
      final skus = skuList
          .map((item) => item['sku'])
          .where((sku) => sku != null && sku.toString().isNotEmpty)
          .toList();
      if (skus.isEmpty) return skuList;
      final typesenseService = getIt<TypesenseService>();
      final items = await typesenseService.searchProducts(
        query: '*',
        page: 1,
        pageSize: skus.length,
        skus: skus.cast<String>(),
      );

      final Map<String, Map<String, dynamic>> skuToData = {
        for (var item in items)
          if (item['sku'] != null)
            item['sku'].toString(): {
              'imageUrl': item['imageUrl'],
              'name': item['name']
            }
      };

      for (var skuObj in skuList) {
        final sku = skuObj['sku']?.toString();
        if (sku != null && skuToData.containsKey(sku)) {
          final data = skuToData[sku]!;
          skuObj['imageUrl'] = data['imageUrl'];
          skuObj['name'] = data['name'];
        }
      }
      return skuList;
    } catch (e) {
      return skuList;
    }
  }

  @override
  Future<OrderEntity?> getOrderDetails(String orderId) async {
    try {
      final response = await ServiceApiClient.getFromOms(
        endUrl: EndUrl.getOrderDetails,
        queryParameters: {
          'order_id': orderId,
        },
        tag: 'GetOrderDetails',
      );

      if (response?.data == null) {
        throw Exception('No data received from API');
      }

      var orderData = response!.data as Map<String, dynamic>;
      if (orderData['items'] != null && orderData['items'] is List) {
        final itemsData = await getSKUDataFromTS(orderData['items'] as List);
        orderData['items'] = itemsData;
      }
      final orderModel = OrderModel.fromJson(orderData);
      final orderEntity = OrderMapper.toEntity(orderModel);

      return orderEntity;
    } catch (e) {
      throw Exception('Failed to fetch order details: $e');
    }
  }

  @override
  Future<bool> cancelOrder(String orderId) async {
    try {
      final response = await ServiceApiClient.post(
        endUrl: EndUrl.cancelOrder,
        data: {
          'order_id': orderId,
        },
        tag: 'CancelOrder',
      );

      return response?.statusCode == 200;
    } catch (e) {
      throw Exception('Failed to cancel order: $e');
    }
  }

  @override
  Future<Response?> verifyPayment({
    required String razorpayOrderId,
    required String razorpayPaymentId,
    required String razorpaySignature,
    required String omsOrderId,
  }) async {
    try {
      final payload = {
        'razorpay_order_id': razorpayOrderId,
        'razorpay_payment_id': razorpayPaymentId,
        'razorpay_signature': razorpaySignature,
        'oms_order_id': omsOrderId,
      };

      // Call the verify_payment API endpoint
      return await ServiceApiClient.post(
        endUrl: EndUrl.verifyPayment,
        data: payload,
        tag: 'VerifyPayment',
      );
    } catch (e) {
      throw Exception('Failed to verify payment: $e');
    }
  }
}
