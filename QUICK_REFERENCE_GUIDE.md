# B2C App - Quick Reference Guide

## 🚀 Getting Started for New Developers

### Project Structure Overview
```
lib/
├── app/                    # App-level configuration and BLoC
├── core/                   # Shared utilities, services, themes
├── data/                   # Data layer (models, repositories)
├── domain/                 # Business logic (entities, use cases)
├── features/               # Feature-specific modules
├── l10n/                   # Localization files
├── routes/                 # App routing configuration
├── widgets/                # Reusable UI components
└── main.dart              # App entry point
```

### Key Features by Directory

#### `/features/auth/`
- **Purpose**: User authentication and login
- **Key Files**: `login_bloc.dart`, `login_screen.dart`
- **Functionality**: Phone OTP authentication, user session management

#### `/features/home/<USER>
- **Purpose**: Main dashboard and navigation
- **Key Files**: `home_bloc.dart`, `dashboard_screen.dart`
- **Functionality**: Product recommendations, banners, category navigation

#### `/features/products/`
- **Purpose**: Product catalog and details
- **Key Files**: `product_listing_bloc.dart`, `product_detail_screen.dart`
- **Functionality**: Product browsing, details view, variants

#### `/features/cart/`
- **Purpose**: Shopping cart and checkout
- **Key Files**: `cart_bloc.dart`, `cart_screen.dart`
- **Functionality**: Cart management, order placement, payment

#### `/features/search/`
- **Purpose**: Product search functionality
- **Key Files**: `search_bloc.dart`, `typesense_service.dart`
- **Functionality**: Advanced search, suggestions, filters

#### `/features/location/`
- **Purpose**: Location services and address management
- **Key Files**: `location_bloc.dart`, `address_services.dart`
- **Functionality**: GPS location, address management, service area check

#### `/features/order/`
- **Purpose**: Order management and history
- **Key Files**: `order_history_screen.dart`
- **Functionality**: Order tracking, history, reordering

#### `/features/profile/`
- **Purpose**: User profile and settings
- **Key Files**: `profile_bloc.dart`, `profile_screen.dart`
- **Functionality**: Profile management, settings, logout

## 🔧 Core Services

### Authentication Service
```dart
// Location: lib/data/repositories/auth_repository_impl.dart
// Purpose: Firebase Auth integration with OTP verification
```

### Typesense Search Service
```dart
// Location: lib/features/search/services/typesense_service.dart
// Purpose: Fast product search with typo tolerance
```

### Address Service
```dart
// Location: lib/features/location/services/adress_services.dart
// Purpose: Location detection and address management
```

### Cart Service
```dart
// Location: lib/features/cart/bloc/cart_bloc.dart
// Purpose: Shopping cart state management
```

## 🎯 Key User Flows

### 1. User Registration/Login
1. User enters phone number
2. OTP sent via Firebase Auth
3. User verifies OTP
4. Profile setup (if first time)
5. Redirect to home

### 2. Product Discovery
1. Browse categories on home screen
2. Use search for specific products
3. Apply filters and sorting
4. View product details
5. Add to cart

### 3. Checkout Process
1. Review cart items
2. Select delivery address
3. Choose payment method
4. Validate stock availability
5. Place order
6. Payment processing (if online)
7. Order confirmation

### 4. Order Management
1. View order history
2. Track order status
3. View order details
4. Reorder previous items
5. Cancel orders (if applicable)

## 🛠️ Development Guidelines

### State Management (BLoC Pattern)
```dart
// Event Definition
abstract class FeatureEvent {}

// State Definition  
abstract class FeatureState {}

// BLoC Implementation
class FeatureBloc extends Bloc<FeatureEvent, FeatureState> {
  // Handle events and emit states
}
```

### API Integration
```dart
// Use ServiceApiClient for API calls
final response = await ServiceApiClient.getFromOms(
  endUrl: EndUrl.someEndpoint,
  tag: 'SomeOperation',
);
```

### Error Handling
```dart
try {
  // Operation
} catch (e) {
  LogMessage.p('Error: $e');
  // Handle error gracefully
}
```

### Localization
```dart
// Use context.l10n for localized strings
Text(context.l10n.someKey)
```

## 🔍 Debugging Tips

### Common Issues & Solutions

1. **Search not working**: Check Typesense configuration in Remote Config
2. **Location not detected**: Verify GPS permissions and Google Places API key
3. **Payment failing**: Check Razorpay configuration and test keys
4. **Cart not persisting**: Verify local storage implementation
5. **Images not loading**: Check image URLs and caching service

### Useful Debug Commands
```bash
# View logs
flutter logs

# Clear app data
flutter clean

# Rebuild with fresh dependencies
flutter pub get && flutter run
```

### Key Log Tags to Monitor
- `TypesenseService`: Search functionality
- `CartBloc`: Cart operations
- `LocationService`: GPS and address services
- `AuthRepository`: Authentication flows
- `PaymentService`: Payment processing

## 📱 Testing

### Running Tests
```bash
# Unit tests
flutter test

# Widget tests
flutter test test/widget_test.dart

# Integration tests
flutter drive --target=test_driver/app.dart
```

### Test Coverage Areas
- Authentication flows
- Cart operations
- Search functionality
- Location services
- Payment processing
- Order management

## 🚀 Deployment

### Build Commands
```bash
# Development build
./scripts/build_dev.sh

# Staging build
./scripts/build_staging.sh

# Production build
./scripts/build_production.sh
```

### Environment Configuration
- Development: Test APIs, debug logging enabled
- Staging: Production-like APIs, limited logging
- Production: Live APIs, minimal logging, optimized

## 📋 Checklist for New Features

### Before Development
- [ ] Understand business requirements
- [ ] Review existing similar features
- [ ] Plan state management approach
- [ ] Design API integration points
- [ ] Consider error handling scenarios

### During Development
- [ ] Follow BLoC pattern for state management
- [ ] Implement proper error handling
- [ ] Add appropriate logging
- [ ] Consider offline scenarios
- [ ] Implement loading states

### Before Release
- [ ] Write unit tests
- [ ] Test on different devices
- [ ] Verify accessibility
- [ ] Check performance impact
- [ ] Update documentation

## 🔗 Important Dependencies

### Core Dependencies
- `flutter_bloc`: State management
- `go_router`: Navigation
- `firebase_auth`: Authentication
- `cloud_firestore`: Database
- `geolocator`: Location services
- `razorpay_flutter`: Payments

### Development Dependencies
- `flutter_test`: Testing framework
- `mockito`: Mocking for tests
- `flutter_launcher_icons`: App icons
- `flutter_native_splash`: Splash screen

## 📞 Support & Resources

### Documentation
- Flutter: https://flutter.dev/docs
- Firebase: https://firebase.google.com/docs
- BLoC: https://bloclibrary.dev

### Internal Resources
- API Documentation: Check with backend team
- Design System: Refer to Figma designs
- Testing Guidelines: See test/ directory examples

This quick reference guide provides essential information for developers to quickly understand and work with the B2C app codebase.
