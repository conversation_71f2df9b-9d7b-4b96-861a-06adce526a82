export 'location_event.dart';
export 'location_state.dart';

import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'package:get_it/get_it.dart';
import '../../../../data/models/adress_model.dart';
import '../../../../features/search/services/typesense_service.dart';
import '../../services/adress_services.dart';
import '../../services/locaion_services.dart';
import 'location_event.dart';
import 'location_state.dart';

class LocationBloc extends Bloc<LocationEvent, LocationState> {
  final AddressService addressService;
  final LocationService locationService;

  double? _lastInitializedLatitude;
  double? _lastInitializedLongitude;
  DateTime? _lastInitializationTime;

  LocationBloc({
    required this.addressService,
    required this.locationService,
  }) : super(const LocationState.initial()) {
    on<LocationEvent>(
      (event, emit) => event.map(
        started: (_) => _onStarted(emit),
        refreshLocation: (e) => _onRefreshLocation(e.tempAddress, emit),
        requestPermissionAndDetect: (_) => _onRequestPermissionAndDetect(emit),
      ),
    );

    // Listen for manual address changes
    addressService.setOnAddressChanged((address) {
      add(const LocationEvent.refreshLocation());
    });
  }

  Future<void> _onStarted(Emitter<LocationState> emit) async {
    emit(const LocationState.loading());
    try {
      final isAuthenticated = addressService.isAuthenticated;

      if (isAuthenticated) {
        final selectedAddress =
            await addressService.getSelectedAddressFromStorage();

        if (selectedAddress != null) {
          await _initializeTypesenseWithCoordinates(
              selectedAddress.latitude as double,
              selectedAddress.longitude as double);

          final typesenseService = GetIt.instance<TypesenseService>();
          if (typesenseService.isLocationServiceable) {
            emit(LocationState.loaded(selectedAddress));
          } else {
            emit(LocationState.notServiceable(selectedAddress));
          }
          return;
        }
      }
      add(const LocationEvent.requestPermissionAndDetect());
    } catch (e) {
      emit(LocationState.error('Failed to load location.'));
    }
  }

  Future<void> _onRequestPermissionAndDetect(
      Emitter<LocationState> emit) async {
    emit(const LocationState.loading());

    try {
      // Check if we already have permission
      final hasPermission = await locationService.checkLocationPermission();

      if (!hasPermission) {
        // Request permission on first launch
        await locationService.requestLocationPermission();
      }

      // Just detect location without saving (for permission and positioning)
      await locationService.getCurrentPosition();

      // Always fall back to refresh location to handle existing addresses
      add(const LocationEvent.refreshLocation());
    } catch (e) {
      // If anything fails, fall back to refresh location
      add(const LocationEvent.refreshLocation());
    }
  }

  Future<void> _onRefreshLocation(
      AddressModel? tempAdress, Emitter<LocationState> emit) async {
    emit(const LocationState.loading());

    try {
      // Always use cached selected address (respects manual selection)
      final selectedAddress = addressService.getCurrentSelectedAddressSync();
      if (selectedAddress != null) {
        // Initialize TypesenseService with coordinates from selected address
        await _initializeTypesenseWithCoordinates(
            selectedAddress.latitude as double,
            selectedAddress.longitude as double);

        // Check if the location is serviceable
        final typesenseService = GetIt.instance<TypesenseService>();
        if (typesenseService.isLocationServiceable) {
          emit(LocationState.loaded(selectedAddress));
        } else {
          emit(LocationState.notServiceable(selectedAddress));
        }
        return;
      }

      // If no cached address, do fresh detection
      final detectedAddress = await addressService.getSelectedAddress();
      if (detectedAddress != null) {
        // Initialize TypesenseService with coordinates from detected address
        await _initializeTypesenseWithCoordinates(
            detectedAddress.latitude as double,
            detectedAddress.longitude as double);

        // Check if the location is serviceable
        final typesenseService = GetIt.instance<TypesenseService>();
        if (typesenseService.isLocationServiceable) {
          emit(LocationState.loaded(detectedAddress));
        } else {
          emit(LocationState.notServiceable(detectedAddress));
        }
        return;
      }

      if (tempAdress != null) {
        await _initializeTypesenseWithCoordinates(
            tempAdress.latitude as double, tempAdress.longitude as double);

        // Check if the location is serviceable
        final typesenseService = GetIt.instance<TypesenseService>();
        if (typesenseService.isLocationServiceable) {
          emit(LocationState.loaded(tempAdress));
        } else {
          emit(LocationState.notServiceable(tempAdress));
        }
        return;
      }

      // No saved address within 500m - show current location details
      final currentPosition = await locationService.getCurrentPosition();
      if (currentPosition != null) {
        final placemarks = await locationService.getAddressFromCoordinates(
          currentPosition.latitude,
          currentPosition.longitude,
        );
        if (placemarks != null && placemarks.isNotEmpty) {
          final address = _createTempAddress(currentPosition, placemarks.first);

          // Initialize TypesenseService with coordinates from current position
          await _initializeTypesenseWithCoordinates(
              currentPosition.latitude, currentPosition.longitude);

          // Check if the location is serviceable
          final typesenseService = GetIt.instance<TypesenseService>();
          if (typesenseService.isLocationServiceable) {
            emit(LocationState.loaded(address));
          } else {
            emit(LocationState.notServiceable(address));
          }
          return;
        }
      }

      // Fallback to default coordinates (Delhi)
      debugPrint(
          'LocationBloc: No address available, using default coordinates');
      emit(const LocationState.error('No address available.'));
    } catch (e) {
      debugPrint('LocationBloc: Error while refreshing location: $e');
      emit(LocationState.error('Error while refreshing location.'));
    }
  }

  AddressModel _createTempAddress(Position pos, Placemark pm) {
    final fullAddress = [
      pm.street,
      pm.subLocality,
      pm.locality,
      pm.administrativeArea,
      pm.postalCode
    ].where((e) => (e ?? '').isNotEmpty).join(', ');

    final addressLine1 = [
      pm.street,
      pm.subLocality,
    ].where((e) => (e ?? '').isNotEmpty).join(', ');

    return AddressModel(
      id: 'temp_${DateTime.now().millisecondsSinceEpoch}',
      fullAddress: fullAddress,
      addressLine1: addressLine1,
      city: pm.locality ?? '',
      state: pm.administrativeArea ?? '',
      pincode: pm.postalCode ?? '',
      latitude: pos.latitude,
      longitude: pos.longitude,
      addressType: 'current',
      isDefault: true,
    );
  }

  /// Reset location state - useful for user logout scenarios
  void resetLocation() {
    add(const LocationEvent.started());
  }

  /// Reload default address - useful when user adds/updates addresses
  void reloadDefaultAddress() {
    add(const LocationEvent.started());
  }

  /// Refresh UI with current selected address
  void refreshUI() {
    add(const LocationEvent.refreshLocation()); // Will use cached data first
  }

  /// Initialize TypesenseService with coordinates
  /// This ensures store detection happens with valid coordinates
  Future<void> _initializeTypesenseWithCoordinates(
      double latitude, double longitude) async {
    try {
      final now = DateTime.now();
      if (_lastInitializedLatitude == latitude &&
          _lastInitializedLongitude == longitude &&
          _lastInitializationTime != null &&
          now.difference(_lastInitializationTime!).inSeconds < 2) {
        debugPrint(
            'LocationBloc: Skipping duplicate initialization with same coordinates');
        return;
      }

      _lastInitializedLatitude = latitude;
      _lastInitializedLongitude = longitude;
      _lastInitializationTime = now;

      debugPrint(
          'LocationBloc: Initializing TypesenseService with coordinates: $latitude, $longitude');
      final typesenseService = GetIt.instance<TypesenseService>();
      await typesenseService.reinitializeWithCoordinates(latitude, longitude);
      debugPrint('LocationBloc: TypesenseService initialization completed');
    } catch (e) {
      debugPrint('LocationBloc: Error initializing TypesenseService: $e');
    }
  }

  @override
  Future<void> close() {
    // Clean up any location listeners or resources
    // LocationService and AddressService cleanup handled by DI container
    return super.close();
  }
}
