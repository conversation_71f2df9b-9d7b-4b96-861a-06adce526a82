import 'package:flutter/material.dart';
import 'package:rozana/core/utils/logger.dart';
import 'package:webview_flutter/webview_flutter.dart';

class ChatScreen extends StatefulWidget {
  final String customerCode;
  final String iv;

  const ChatScreen({super.key, required this.customerCode, required this.iv});

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  String? chatUrl;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _prepareAndLoadChat();
  }

  void _prepareAndLoadChat() {
    // Encrypt the customer code on the frontend
    // final encryptedData =
    //     EncryptionHelper.encryptCustomerCode(widget.customerCode);
    // final encryptedCustomerCode = encryptedData['encryptedCustomerCode']!;
    // final iv = encryptedData['iv']!;

    // Static parameters from the documentation
    const baseUrl =
        'https://selfserveapp.kapturecrm.com/cb-v1/web-view/webview_chat.html';
    const supportKey = '1a1c974cd9eb9b77f6643d3b1f62a731f34840191471081149';
    const chatFor = 'TICKET';
    const server = 'Indian';
    const serverHost = 'ms-noauth';
    const scriptType = 'RNF';

    // Construct the final URL with all parameters
    final uri = Uri.parse(baseUrl).replace(queryParameters: {
      'data-supportkey': supportKey,
      'chat-for': chatFor,
      'data-server': server,
      'server-host': serverHost,
      'script_type': scriptType,
      'customer_code': widget.customerCode,
      'iv': widget.iv,
    });

    LogMessage.l("FGHJULOIYTGYHUIKO::::::::::::::${uri}");

    setState(() {
      chatUrl = uri.toString();
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Chat Support'),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : chatUrl != null
              ? WebViewWidget(
                  controller: WebViewController()
                    ..setJavaScriptMode(JavaScriptMode.unrestricted)
                    ..loadRequest(Uri.parse(chatUrl!)),
                )
              : const Center(child: Text('Failed to load chat.')),
    );
  }
}
