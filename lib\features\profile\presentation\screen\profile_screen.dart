import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:rozana/core/services/app_preferences_service.dart';
import 'package:rozana/core/themes/color_schemes.dart';
import 'package:rozana/core/extensions/localization_extension.dart';
import 'package:rozana/core/blocs/language_bloc/language_bloc.dart';
import 'package:rozana/routes/app_router.dart';
import 'package:rozana/widgets/custom_button.dart';
import '../widgets/profile_setup_modal.dart';

import '../../bloc/profile_bloc.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  @override
  void initState() {
    super.initState();
    // Load user data and address count through bloc
    context.read<ProfileBloc>().add(const ProfileEvent.loadUserData());
  }

  @override
  Widget build(BuildContext context) {
    return Builder(
      builder: (context) {
        return Scaffold(
          backgroundColor: AppColors.neutral100,
          appBar: AppBar(
            title: Text(
              context.l10n.myProfile,
              style:  TextStyle(
                color: AppColors.neutral600,
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            centerTitle: true,
            elevation: 0,
            backgroundColor: AppColors.neutral100,
            iconTheme:  IconThemeData(
              color: AppColors.neutral600, // Makes back button visible
            ),
          ),
          body: BlocListener<ProfileBloc, ProfileState>(
            listener: (context, state) {
              state.mapOrNull(
                error: (errorState) {
                  if (errorState.message.contains('logout')) {
                    // Handle logout success by navigating to login
                    context.go(RouteNames.login);
                  }
                },
              );
            },
            child: BlocBuilder<ProfileBloc, ProfileState>(
              builder: (context, state) {
                return state.map(
                  initial: (_) =>  Center(
                      child: CircularProgressIndicator(
                    color: AppColors.primary,
                  )),
                  loading: (_) =>  Center(
                      child: CircularProgressIndicator(
                    color: AppColors.primary,
                  )),
                  loaded: (loadedState) => _buildProfileContent(
                    context,
                    loadedState.userName,
                    loadedState.userEmail,
                    loadedState.userGender,
                    loadedState.addressCount,
                    false, // Not loading for UI interaction
                  ),
                  error: (errorState) =>
                      _buildErrorState(context, errorState.message),
                );
              },
            ),
          ),
        );
      },
    );
  }

  Widget _buildErrorState(BuildContext context, String errorMessage) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: AppColors.error,
            ),
            const SizedBox(height: 16),
            Text(
              context.l10n.somethingWentWrong,
              style:  TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: AppColors.neutral600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              errorMessage,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                color: AppColors.neutral400,
              ),
            ),
            const SizedBox(height: 24),
            AppButton(
              text: context.l10n.retry,
              onPressed: () {
                context
                    .read<ProfileBloc>()
                    .add(const ProfileEvent.loadUserData());
              },
              backgroundColor: AppColors.primary,
              width: 120,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileContent(
    BuildContext context,
    String userName,
    String userEmail,
    String userGender,
    int addressCount,
    bool isLoading,
  ) {
    return RefreshIndicator(
      onRefresh: () async {
        context.read<ProfileBloc>().add(const ProfileEvent.loadUserData());
        // Wait a bit for the bloc to process
        await Future.delayed(const Duration(milliseconds: 500));
      },
      color: AppColors.primary,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            const SizedBox(height: 20),
            _buildProfileAvatar(userName, userEmail),
            const SizedBox(height: 24),
            _buildSettingsBody(addressCount),
            const SizedBox(height: 20),
            _buildLogoutButton(context, isLoading),
            const SizedBox(height: 20),
            _buildAccountDeleteButton(context, isLoading),
            const SizedBox(height: 40),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileAvatar(String userName, String userEmail) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 6.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          CircleAvatar(
            radius: 30,
            backgroundColor: AppColors.primary.withValues(alpha: 0.2),
            child:  Icon(
              Icons.person,
              size: 40,
              color: AppColors.primary,
            ),
          ),
          const SizedBox(width: 16),
          _buildUserDetails(userName, userEmail),
        ],
      ),
    );
  }

  Widget _buildUserDetails(String userName, String userEmail) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          userName,
          style:  TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AppColors.neutral600),
        ),
        const SizedBox(height: 4),
        Text(
          userEmail,
          style:  TextStyle(
            fontSize: 14,
            color: AppColors.neutral400,
          ),
        ),
      ],
    );
  }

  Widget _buildSettingsTile({
    required IconData icon,
    required String title,
    String? subtitle,
    required VoidCallback onTap,
  }) {
    return ListTile(
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: AppColors.primary.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          icon,
          size: 16,
          color: AppColors.primary,
        ),
      ),
      title: Text(
        title,
        style:  TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: AppColors.neutral600,
        ),
      ),
      subtitle: subtitle != null
          ? Text(
              subtitle,
              style:  TextStyle(
                fontSize: 12,
                color: AppColors.neutral400,
              ),
            )
          : null,
      trailing: Icon(
        Icons.chevron_right,
        color: AppColors.neutral400,
      ),
      onTap: onTap,
    );
  }

  Widget _buildSettingsSection({
    required String header,
    required List<Widget> tiles,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            header,
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Container(
            decoration: BoxDecoration(
              color: AppColors.neutral100,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                  color: AppColors.neutral200.withValues(alpha: 0.3)),
              boxShadow: [
                BoxShadow(
                  color: AppColors.neutral800.withValues(alpha: 0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: Column(
              children: tiles
                  .expand((tile) => [
                        tile,
                        const Divider(
                            height: 1,
                            thickness: 0.1,
                            indent: 16,
                            endIndent: 16),
                      ])
                  .toList()
                ..removeLast(), // remove trailing divider
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsBody(int addressCount) {
    return Column(
      children: [
        _buildSettingsSection(
          header: "Your Information",
          tiles: [
            _buildSettingsTile(
              icon: Icons.shopping_bag_outlined,
              title: context.l10n.yourOrders,
              onTap: () {
                context.replace(RouteNames.orders);
              },
            ),
            _buildSettingsTile(
              icon: Icons.support_agent,
              title: context.l10n.helpSupport,
              onTap: () {
                final existingUserJson = AppPreferences.getUserdata();
                Map<String, dynamic> userData = {};

                if (existingUserJson != null && existingUserJson.isNotEmpty) {
                  userData = jsonDecode(existingUserJson);
                }
                context.push(RouteNames.support, extra: {
                  'customer_id': userData['enc_cus_code'],
                  'iv': userData['iv']
                });
              },
            ),
            _buildSettingsTile(
              icon: Icons.currency_rupee,
              title: context.l10n.refunds,
              onTap: () {},
            ),
            _buildSettingsTile(
              icon: Icons.location_on_outlined,
              title: context.l10n.savedAddresses,
              subtitle:
                  "$addressCount ${addressCount == 1 ? context.l10n.address : context.l10n.addresses}",
              onTap: () {
                context.push(RouteNames.addresses).then((_) {
                  if (mounted) {
                    context
                        .read<ProfileBloc>()
                        .add(const ProfileEvent.loadAddressCount());
                  }
                });
              },
            ),
            _buildSettingsTile(
              icon: Icons.person_outline,
              title: context.l10n.profile,
              onTap: () async {
                final state = context.read<ProfileBloc>().state;
                String? name;
                String? email;
                state.mapOrNull(
                  loaded: (loadedState) {
                    name = loadedState.userName;
                    email = loadedState.userEmail;
                  },
                );
                final updated = await showProfileSetupModal(
                  context,
                  initialName: name,
                  initialEmail: email,
                  isEdit: true,
                );
                if (updated == true && mounted) {
                  context
                      .read<ProfileBloc>()
                      .add(const ProfileEvent.loadUserData());
                }
              },
            ),
            _buildSettingsTile(
              icon: Icons.payment,
              title: context.l10n.paymentManagement,
              onTap: () {},
            ),
            _buildSettingsTile(
              icon: Icons.info_outline,
              title: context.l10n.aboutUs,
              onTap: () {
                context.push(RouteNames.about);
              },
            ),
            _buildSettingsTile(
              icon: Icons.privacy_tip_outlined,
              title: context.l10n.privacyPolicy,
              onTap: () {
                context.push(RouteNames.privacyPolicy);
              },
            ),
            _buildSettingsTile(
              icon: Icons.privacy_tip_outlined,
              title: context.l10n.termsAndConditions,
              onTap: () {
                context.push(RouteNames.termsConditions);
              },
            ),
            _buildSettingsTile(
              icon: Icons.notifications_outlined,
              title: context.l10n.notifications,
              onTap: () {
                context.push(RouteNames.notifications);
              },
            ),
            _buildLanguageSwitcher(),
          ],
        ),
      ],
    );
  }

  Widget _buildLogoutButton(BuildContext context, bool isLoading) {
    return AppButton(
      text: context.l10n.logout,
      onPressed: () {
        context.read<ProfileBloc>().add(const ProfileEvent.logout());
      },
      isLoading: isLoading,
      width: double.infinity,
    );
  }

  Widget _buildAccountDeleteButton(BuildContext context, bool isLoading) {
    return AppButton(
      text: context.l10n.deleteMyAccount,
      onPressed: () {},
      backgroundColor: AppColors.error,
    );
  }

  Widget _buildLanguageSwitcher() {
    return ListTile(
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: AppColors.primary.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child:  Icon(
          Icons.language,
          size: 20,
          color: AppColors.primary,
        ),
      ),
      title: Text(
        context.l10n.language,
        style:  TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: AppColors.neutral600,
        ),
      ),
      trailing: BlocBuilder<LanguageBloc, LanguageState>(
        builder: (context, languageState) {
          final currentLanguage = languageState.mapOrNull(
                loaded: (state) => state.languageCode,
              ) ??
              'en';

          return DropdownButton<String>(
            value: currentLanguage,
            underline: const SizedBox(),
            icon:  Icon(
              Icons.arrow_drop_down,
              color: AppColors.neutral400,
            ),
            items: [
              DropdownMenuItem(
                value: 'en',
                child: Text(
                  context.l10n.english,
                  style:  TextStyle(
                    fontSize: 14,
                    color: AppColors.neutral600,
                  ),
                ),
              ),
              DropdownMenuItem(
                value: 'hi',
                child: Text(
                  context.l10n.hindi,
                  style:  TextStyle(
                    fontSize: 14,
                    color: AppColors.neutral600,
                  ),
                ),
              ),
            ],
            onChanged: (String? newLanguage) {
              if (newLanguage != null && newLanguage != currentLanguage) {
                context
                    .read<LanguageBloc>()
                    .add(LanguageEvent.changeLanguage(newLanguage));
              }
            },
          );
        },
      ),
    );
  }
}
